#include "TracktionWrapper/TracktionWrapperUtils.h"
#include "TracktionWrapper/EditWrapper.h"
#include "TracktionWrapper/TransportWrapper.h"

#include <QDebug>

std::unique_ptr<TransportWrapper> TracktionWrapperUtils::createTransportWrapper(EditWrapper* editWrapper)
{
    qDebug() << "TracktionWrapperUtils::createTransportWrapper - Creating TransportWrapper from EditWrapper:" << editWrapper;

    if (!isEditWrapperValid(editWrapper)) {
        qDebug() << "TracktionWrapperUtils::createTransportWrapper - EditWrapper is invalid, returning nullptr";
        return nullptr;
    }

    try {
        // Extract the TransportControl and Edit from the EditWrapper
        tracktion::engine::TransportControl* transportControl = getTransportControlFromWrapper(editWrapper);
        tracktion::engine::Edit* edit = getEditFromWrapper(editWrapper);

        if (!transportControl || !edit) {
            qWarning() << "TracktionWrapperUtils::createTransportWrapper - Failed to extract TransportControl or Edit from EditWrapper";
            return nullptr;
        }

        // Create the TransportWrapper using the same pattern as in AppModel::createEditDependentModels
        auto transportWrapper = std::make_unique<TransportWrapper>(*transportControl, *edit);
        
        qDebug() << "TracktionWrapperUtils::createTransportWrapper - Successfully created TransportWrapper";
        return transportWrapper;

    } catch (const std::exception& e) {
        qCritical() << "TracktionWrapperUtils::createTransportWrapper - Exception occurred:" << e.what();
        return nullptr;
    } catch (...) {
        qCritical() << "TracktionWrapperUtils::createTransportWrapper - Unknown exception occurred";
        return nullptr;
    }
}

bool TracktionWrapperUtils::isEditWrapperValid(EditWrapper* editWrapper)
{
    if (!editWrapper) {
        return false;
    }

    try {
        // Check if the EditWrapper has a valid Edit
        tracktion::engine::Edit* edit = getEditFromWrapper(editWrapper);
        return edit != nullptr;
    } catch (...) {
        // If any exception occurs during validation, consider it invalid
        return false;
    }
}

tracktion::engine::Edit* TracktionWrapperUtils::getEditFromWrapper(EditWrapper* editWrapper)
{
    if (!editWrapper) {
        return nullptr;
    }

    try {
        // Use the EditWrapper's getEdit method to get a reference to the Edit
        return &editWrapper->getEdit();
    } catch (const std::exception& e) {
        qWarning() << "TracktionWrapperUtils::getEditFromWrapper - Exception getting Edit:" << e.what();
        return nullptr;
    } catch (...) {
        qWarning() << "TracktionWrapperUtils::getEditFromWrapper - Unknown exception getting Edit";
        return nullptr;
    }
}

tracktion::engine::TransportControl* TracktionWrapperUtils::getTransportControlFromWrapper(EditWrapper* editWrapper)
{
    if (!editWrapper) {
        return nullptr;
    }

    try {
        // Use the EditWrapper's getTransportControl method to get a reference to the TransportControl
        return &editWrapper->getTransportControl();
    } catch (const std::exception& e) {
        qWarning() << "TracktionWrapperUtils::getTransportControlFromWrapper - Exception getting TransportControl:" << e.what();
        return nullptr;
    } catch (...) {
        qWarning() << "TracktionWrapperUtils::getTransportControlFromWrapper - Unknown exception getting TransportControl";
        return nullptr;
    }
}
