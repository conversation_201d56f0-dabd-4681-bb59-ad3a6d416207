#pragma once

#include <memory>

// Forward declarations
class EditWrapper;
class TransportWrapper;

namespace tracktion
{
    inline namespace engine
    {
        class Edit;
        class TransportControl;
    }
}

/**
 * @brief Utility class for creating Tracktion wrapper objects from other wrappers
 * 
 * This class provides static utility methods to create wrapper objects from existing
 * wrapper objects, following consistent patterns throughout the codebase.
 */
class TracktionWrapperUtils
{
public:
    /**
     * @brief Create a TransportWrapper from an EditWrapper
     * 
     * This utility method extracts the TransportControl and Edit from an EditWrapper
     * and creates a new TransportWrapper instance. This pattern is commonly used
     * when creating edit-dependent models.
     * 
     * @param editWrapper The EditWrapper to extract transport information from
     * @return std::unique_ptr<TransportWrapper> A new TransportWrapper instance, 
     *         or nullptr if editWrapper is null or invalid
     * 
     * @note The returned TransportWrapper takes references to the Edit and TransportControl
     *       owned by the EditWrapper, so the EditWrapper must remain valid for the
     *       lifetime of the returned TransportWrapper.
     */
    static std::unique_ptr<TransportWrapper> createTransportWrapper(EditWrapper* editWrapper);

    /**
     * @brief Check if an EditWrapper is valid and has the necessary components
     * 
     * @param editWrapper The EditWrapper to validate
     * @return bool True if the EditWrapper is valid and has a valid Edit
     */
    static bool isEditWrapperValid(EditWrapper* editWrapper);

    /**
     * @brief Get the Edit reference from an EditWrapper safely
     * 
     * @param editWrapper The EditWrapper to extract the Edit from
     * @return tracktion::engine::Edit* Pointer to the Edit, or nullptr if invalid
     */
    static tracktion::engine::Edit* getEditFromWrapper(EditWrapper* editWrapper);

    /**
     * @brief Get the TransportControl reference from an EditWrapper safely
     * 
     * @param editWrapper The EditWrapper to extract the TransportControl from
     * @return tracktion::engine::TransportControl* Pointer to the TransportControl, or nullptr if invalid
     */
    static tracktion::engine::TransportControl* getTransportControlFromWrapper(EditWrapper* editWrapper);

private:
    // Static utility class - no instances allowed
    TracktionWrapperUtils() = delete;
    ~TracktionWrapperUtils() = delete;
    TracktionWrapperUtils(const TracktionWrapperUtils&) = delete;
    TracktionWrapperUtils& operator=(const TracktionWrapperUtils&) = delete;
};
