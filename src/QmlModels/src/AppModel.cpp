#include "AppModel.h"
#include "EngineModel.h"
#include "TransportModel.h"
#include "EditModel.h"
#include "EditViewModel.h"
#include "ProjectManagerModel.h"
#include "DeviceManagerModel.h"
#include "SelectionManagerModel.h"

#include <QDebug>

#include "TracktionWrapper/TransportWrapper.h"
#include "TracktionWrapper/EditViewWrapper.h"
#include "TracktionWrapper/SelectionManagerWrapper.h"
#include "TracktionWrapper/EditWrapper.h"
#include "TracktionWrapper/TracktionWrapperUtils.h"

AppModel::AppModel(QObject* parent) : QObject(parent)
{
    qDebug() << "AppModel::AppModel - Constructor called.";

    try {
        // Initialize core models first
        initializeCoreModels();

        // Initialize edit-dependent models with nullptr initially
        createEditDependentModels(nullptr);

        // Connect EngineModel's editLoaded signal to update edit-dependent models
        connect(mEngineModel.get(), &EngineModel::editLoaded,
                this, &AppModel::handleEditLoaded, Qt::DirectConnection);

        qDebug() << "AppModel::AppModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "AppModel::AppModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "AppModel::AppModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

AppModel::~AppModel()
{
    qDebug() << "AppModel::~AppModel - Destructor called.";

    try {
        // Proper destruction order: Edit-dependent models first, then core models
        cleanupEditDependentModels();

        // Core models cleanup (automatic via unique_ptr in reverse declaration order)
        mProjectManagerModel.reset();
        mSelectionManagerModel.reset();
        mDeviceManagerModel.reset();
        mEngineModel.reset(); // Engine last

        qDebug() << "AppModel::~AppModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "AppModel::~AppModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "AppModel::~AppModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

void AppModel::setEditModel(std::unique_ptr<EditModel> newEditModel)
{
    if (mEditModel != newEditModel)
    {
        mEditModel = std::move(newEditModel);
        emit editModelChanged();
    }
}

void AppModel::setEditModelFromRawPointer(EditModel* editModel)
{
    // This method is for QML compatibility only
    // It should not be used for transferring ownership
    qWarning() << "AppModel::setEditModelFromRawPointer - This method should not be used for ownership transfer";

    // For QML compatibility, we don't actually change anything here
    // The EditModel should be managed through the smart pointer methods
}

void AppModel::createEditDependentModels(EditWrapper* editWrapper)
{
    qDebug() << "AppModel::createEditDependentModels - Creating/recreating edit-dependent models. Received pointer:" <<
            editWrapper;

    qDebug() << "AppModel::createEditDependentModels - Creating new edit models";

    // Determine if we have a valid edit to work with
    tracktion::engine::Edit* edit = TracktionWrapperUtils::getEditFromWrapper(editWrapper);
    tracktion::engine::TransportControl* transportControl = TracktionWrapperUtils::getTransportControlFromWrapper(editWrapper);

    // Re-instantiate EditViewModel
    mEditViewModel = std::make_unique<EditViewModel>(
            edit
                ? std::make_shared<EditViewWrapper>(
                        *edit, mSelectionManagerModel->getSelectionManagerWrapper()->getSelectionManager())
                : nullptr,
            this
            );

    // Now construct mEditModel with smart pointer
    qDebug() << "AppModel::createEditDependentModels - Before constructing EditModel, editWrapper pointer:" <<
            editWrapper;
    mEditModel.reset(); // Clean up old model
    mEditModel = std::make_unique<EditModel>(editWrapper, // EditWrapper ownership remains with EngineModel
                                           mSelectionManagerModel.get(),
                                           mEditViewModel.get(),
                                           this);
    qDebug() << "AppModel::createEditDependentModels - After constructing EditModel, editWrapper pointer:" <<
            editWrapper;


    // Re-instantiate TransportModel using TracktionWrapperUtils
    mTransportModel = std::make_unique<TransportModel>(
            TracktionWrapperUtils::createTransportWrapper(editWrapper),
            mEditModel.get(), // Pass EditModel raw pointer for non-owning reference
            this
            );
    emit transportModelChanged();

    // Re-instantiate ProjectManagerModel
    mProjectManagerModel = std::make_unique<ProjectManagerModel>(mEngineModel.get(), mEditModel.get(), this);
}

void AppModel::handleEditLoaded(EditWrapper* newEditWrapper)
{
    qDebug() << "AppModel::handleEditLoaded - New EditWrapper received. Pointer: " << newEditWrapper << ".";
    if (!newEditWrapper)
    {
        qWarning() << "AppModel::handleEditLoaded - Received nullptr. Skipping edit setup.";
        return;
    }

    // Create edit-dependent models with the new EditWrapper
    createEditDependentModels(newEditWrapper);
    emit editModelChanged();
}

// Helper methods implementation
void AppModel::initializeCoreModels()
{
    try {
        // Initialize core engine model first
        mEngineModel = std::make_unique<EngineModel>(this);

        // Initialize device manager (depends on engine)
        mDeviceManagerModel = std::make_unique<DeviceManagerModel>(mEngineModel->getEngineWrapper(), this);

        // Initialize selection manager (depends on engine)
        mSelectionManagerModel = std::make_unique<SelectionManagerModel>(
                std::make_unique<SelectionManagerWrapper>(mEngineModel->getEngineWrapper()->getEngine()),
                this
                );

        qDebug() << "AppModel::initializeCoreModels - Core models initialized successfully";
    } catch (const std::exception& e) {
        qCritical() << "AppModel::initializeCoreModels - Exception:" << e.what();
        throw; // Re-throw to indicate initialization failure
    }
}

void AppModel::cleanupEditDependentModels()
{
    qDebug() << "AppModel::cleanupEditDependentModels - Cleaning up edit-dependent models";

    // Clear edit-dependent models in proper order
    mTransportModel.reset();
    mEditViewModel.reset();
    mEditModel.reset();

    qDebug() << "AppModel::cleanupEditDependentModels - Edit-dependent models cleaned up";
}
