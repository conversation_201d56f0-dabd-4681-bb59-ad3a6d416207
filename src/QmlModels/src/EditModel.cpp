#include "EditModel.h"
#include "TrackModel.h"
#include <QDebug>

#include "TracktionWrapper/EditWrapper.h"

EditModel::EditModel(EditWrapper* editWrapper,
                     SelectionManagerModel* selectionManagerModel,
                     EditViewModel* editViewModel,
                     QObject* parent) :
    QObject(parent),
    mEditWrapper(editWrapper),
    mSelectionManagerModel(selectionManagerModel),
    mEditViewModel(editViewModel)
{
    qDebug() << "EditModel::EditModel - Constructor called.";
    qDebug() << "EditModel::EditModel - editWrapper parameter pointer:" << editWrapper;

    try {
        // Validate input parameters
        if (!isEditWrapperValid()) {
            qWarning() << "EditModel::EditModel - Invalid EditWrapper provided";
        }

        // Initial population of tracks
        populateTracks();

        // Initialize cached values
        updateCachedState();

        qDebug() << "EditModel::EditModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "EditModel::EditModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "EditModel::EditModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

EditModel::~EditModel()
{
    qDebug() << "EditModel::~EditModel - Destructor called.";

    try {
        // Clean up owned track models
        cleanupTracks();

        qDebug() << "EditModel::~EditModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "EditModel::~EditModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "EditModel::~EditModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

QString EditModel::getName() const
{
    return isEditWrapperValid() ? QString::fromStdString(mEditWrapper->getName()) : "Unnamed Edit";
}

bool EditModel::hasValidEdit() const
{
    return isEditWrapperValid();
}

QList<QObject*> EditModel::getTracks() const
{
    QList<QObject*> result;
    for (const auto& track : mTracks) {
        result.append(track.get());
    }
    return result;
}

int EditModel::getProjectLengthInBars() const
{
    return isEditWrapperValid() ? mEditWrapper->getProjectLengthInBars() : 0;
}

bool EditModel::getIsRecording() const
{
    return mIsRecording;
}

bool EditModel::getIsItemSelected() const
{
    return mIsItemSelected;
}

bool EditModel::getDrawWaveforms() const
{
    return mDrawWaveforms;
}

int EditModel::getTimeSignatureNumerator() const
{
    return isEditWrapperValid() ? mEditWrapper->getTimeSignatureNumerator() : 4;
}

int EditModel::getTimeSignatureDenominator() const
{
    return isEditWrapperValid() ? mEditWrapper->getTimeSignatureDenominator() : 4;
}

void EditModel::populateTracks()
{
    qDebug() << "EditModel::populateTracks - Populating tracks";

    try {
        // Clear existing tracks
        cleanupTracks();

        if (isEditWrapperValid()) {
            for (int i = 0; i < mEditWrapper->getNumTracks(); ++i) {
                if (auto trackWrapper = mEditWrapper->getTrack(i)) {
                    auto trackModel = std::make_unique<TrackModel>(trackWrapper, this);
                    mTracks.push_back(std::move(trackModel));
                }
            }
            emit tracksChanged();
            qDebug() << "EditModel::populateTracks - Successfully populated" << mTracks.size() << "tracks";
        } else {
            qWarning() << "EditModel::populateTracks - Invalid EditWrapper, cannot populate tracks";
        }
    } catch (const std::exception& e) {
        qCritical() << "EditModel::populateTracks - Exception:" << e.what();
        cleanupTracks(); // Ensure cleanup on failure
        emit tracksChanged();
    }
}

EditWrapper* EditModel::getEditWrapper() const
{
    return mEditWrapper;
}

TrackModel* EditModel::getTrack(int index) const
{
    if (index >= 0 && index < mTracks.size()) {
        return mTracks.at(index).get();
    }
    return nullptr;
}

int EditModel::getNumTracks() const
{
    return mTracks.size();
}

void EditModel::addTrack()
{
    if (isEditWrapperValid()) {
        if (auto newTrackWrapper = mEditWrapper->addTrack()) {
            auto trackModel = std::make_unique<TrackModel>(newTrackWrapper, this);
            mTracks.push_back(std::move(trackModel));
            emit tracksChanged();
        }
    }
}

void EditModel::removeTrack(TrackModel* track)
{
    if (isEditWrapperValid() && track) {
        if (auto trackWrapper = track->getTrackWrapper()) {
            mEditWrapper->removeTrack(trackWrapper.get());
        }

        // Remove from our container (need to find by raw pointer)
        auto it = std::find_if(mTracks.begin(), mTracks.end(),
                              [track](const std::unique_ptr<TrackModel>& ptr) {
                                  return ptr.get() == track;
                              });
        if (it != mTracks.end()) {
            mTracks.erase(it);
        }

        emit tracksChanged();
    }
}

void EditModel::saveEdit()
{
    if (isEditWrapperValid()) {
        mEditWrapper->saveEdit(true, true, false);
    }
}

void EditModel::toggleRecord()
{
    if (isEditWrapperValid()) {
        mEditWrapper->toggleRecord();
    }
}

void EditModel::addNewAudioTrack()
{
    if (isEditWrapperValid()) {
        mEditWrapper->addNewAudioTrack();
        populateTracks(); // Re-populate tracks after adding
    }
}

void EditModel::clearAllTracks()
{
    if (isEditWrapperValid()) {
        mEditWrapper->clearAllTracks();
        populateTracks(); // Re-populate tracks after clearing
    }
}

void EditModel::undo()
{
    if (isEditWrapperValid()) {
        mEditWrapper->undo();
    }
}

void EditModel::redo()
{
    if (isEditWrapperValid()) {
        mEditWrapper->redo();
    }
}

void EditModel::moveToFirstNote()
{
    if (isEditWrapperValid()) {
        mEditWrapper->moveToFirstNote();
    }
}

void EditModel::createMidiClip()
{
    if (isEditWrapperValid()) {
        mEditWrapper->createMidiClip();
    }
}

void EditModel::deleteSelectedItem()
{
    if (mSelectionManagerModel)
    {
        mSelectionManagerModel->deleteSelected();
        // After deletion, update selection state
        mIsItemSelected = mSelectionManagerModel->getNumObjectsSelected() > 0;
        emit isItemSelectedChanged();
        populateTracks(); // Update tracks if a track was deleted
    }
}

void EditModel::setDrawWaveforms(bool draw)
{
    if (mEditViewModel) {
        mEditViewModel->setDrawWaveforms(draw);
        mDrawWaveforms = mEditViewModel->getDrawWaveforms(); // Get actual state from wrapper
        emit drawWaveformsChanged();
    }
}

void EditModel::updateEditWrapper(EditWrapper* editWrapper)
{
    qDebug() << "EditModel::updateEditWrapper - Updating EditWrapper from" << mEditWrapper << "to" << editWrapper;

    if (mEditWrapper != editWrapper) {
        mEditWrapper = editWrapper;

        // Re-populate tracks with new EditWrapper
        populateTracks();

        // Update cached state
        updateCachedState();

        // Emit relevant signals
        emit nameChanged();
        emit editValidityChanged();
        emit projectLengthInBarsChanged();
        emit timeSignatureChanged();

        qDebug() << "EditModel::updateEditWrapper - EditWrapper updated successfully";
    }
}

// Helper methods implementation
void EditModel::cleanupTracks()
{
    qDebug() << "EditModel::cleanupTracks - Cleaning up" << mTracks.size() << "tracks";
    mTracks.clear(); // unique_ptr automatically handles cleanup
}

void EditModel::updateCachedState()
{
    if (isEditWrapperValid()) {
        mIsRecording = mEditWrapper->isRecording();
        mProjectLengthInBars = mEditWrapper->getProjectLengthInBars();
    } else {
        mIsRecording = false;
        mProjectLengthInBars = 0;
    }

    if (mSelectionManagerModel) {
        mIsItemSelected = mSelectionManagerModel->getNumObjectsSelected() > 0;
    } else {
        mIsItemSelected = false;
    }
}

bool EditModel::isEditWrapperValid() const
{
    return mEditWrapper != nullptr;
}
