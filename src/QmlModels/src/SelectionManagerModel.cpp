#include "SelectionManagerModel.h"
#include <QDebug> // For qDebug

SelectionManagerModel::SelectionManagerModel(std::unique_ptr<SelectionManagerWrapper> wrapper, QObject* parent) :
    QObject(parent),
    mSelectionManagerWrapper(std::move(wrapper))
{
    qDebug() << "SelectionManagerModel::SelectionManagerModel - Constructor called.";

    try {
        // Setup change listener if wrapper is valid
        if (isSelectionManagerWrapperValid()) {
            mSelectionManagerWrapper->addChangeListener(this);
        }

        qDebug() << "SelectionManagerModel::SelectionManagerModel - Constructor completed successfully.";
    } catch (const std::exception& e) {
        qCritical() << "SelectionManagerModel::SelectionManagerModel - Exception during construction:" << e.what();
        throw; // Re-throw to indicate construction failure
    } catch (...) {
        qCritical() << "SelectionManagerModel::SelectionManagerModel - Unknown exception during construction";
        throw; // Re-throw to indicate construction failure
    }
}

SelectionManagerModel::~SelectionManagerModel()
{
    qDebug() << "SelectionManagerModel::~SelectionManagerModel - Destructor called.";

    try {
        // Remove change listener if wrapper is valid
        if (isSelectionManagerWrapperValid()) {
            mSelectionManagerWrapper->removeChangeListener(this);
        }

        qDebug() << "SelectionManagerModel::~SelectionManagerModel - Destructor completed successfully.";
    } catch (const std::exception& e) {
        qWarning() << "SelectionManagerModel::~SelectionManagerModel - Exception during destruction:" << e.what();
        // Don't re-throw from destructor
    } catch (...) {
        qWarning() << "SelectionManagerModel::~SelectionManagerModel - Unknown exception during destruction";
        // Don't re-throw from destructor
    }
}

SelectionManagerWrapper* SelectionManagerModel::getSelectionManagerWrapper() const
{
    return mSelectionManagerWrapper.get();
}

int SelectionManagerModel::getNumObjectsSelected() const
{
    return isSelectionManagerWrapperValid() ? mSelectionManagerWrapper->getNumObjectsSelected() : 0;
}

bool SelectionManagerModel::hasValidSelectionManager() const
{
    return isSelectionManagerWrapperValid();
}

void SelectionManagerModel::deleteSelected()
{
    if (isSelectionManagerWrapperValid()) {
        mSelectionManagerWrapper->deleteSelected();
    }
}

void SelectionManagerModel::wrapperChanged(void* source)
{
    if (source == mSelectionManagerWrapper.get()) {
        emit selectionChanged();
    }
}

// Helper methods implementation
bool SelectionManagerModel::isSelectionManagerWrapperValid() const
{
    return mSelectionManagerWrapper != nullptr;
}
