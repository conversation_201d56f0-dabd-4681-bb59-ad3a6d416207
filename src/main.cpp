#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QDirIterator>

#include "App.h"

int main(int argc, char* argv[])
{
    Q_INIT_RESOURCE(qml_resources); // Initialize QML resources
    QGuiApplication app(argc, argv);
    std::unique_ptr<QQmlApplicationEngine> applicationEngine = std::make_unique<QQmlApplicationEngine>();

    // Instantiate App, parented to the QGuiApplication to ensure its lifetime
    App application(applicationEngine.get(), &app);

    // Set the App instance as a context property before loading QML.
    applicationEngine->rootContext()->setContextProperty("AppInstance", &application);

    // Add QML import path for resources
    applicationEngine->addImportPath("qrc:/");

    // Debug resource content
    // qDebug() << "All resource files:"; // Commented out debug output
    // QDirIterator it(":/", QDirIterator::Subdirectories);
    // while (it.hasNext()) {
    //     qDebug() << it.next();
    // }

    qmlRegisterType<App>("com.yourcompany.qmldaw", 1, 0, "App"); // Register App type
    const QUrl url(QStringLiteral("qrc:/qml/MainView.qml")); // Load MainView.qml
    QMetaObject::Connection connection = QObject::connect(
            applicationEngine.get(), &QQmlApplicationEngine::objectCreated, &app,
            [url, &application](const QObject* obj, const QUrl& objUrl) // Capture application by reference
            {
                if (!obj && url == objUrl)
                    QCoreApplication::exit(-1);
            },
            Qt::QueuedConnection);
    applicationEngine->load(url);

    const int result = app.exec();

    // Disconnect the signal to prevent further QML object creation callbacks
    QObject::disconnect(connection);

    // Explicitly destroy QML root objects to trigger Component.onDestruction
    // and ensure QML cleanup before C++ objects are destroyed.
    if (!applicationEngine->rootObjects().isEmpty())
    {
        applicationEngine->rootObjects().first()->deleteLater();
    }

    applicationEngine->clearComponentCache();
    applicationEngine.reset(); // Explicitly destroy the QML engine

    Q_CLEANUP_RESOURCE(qml_resources); // Clean up QML resources

    return result;
}
